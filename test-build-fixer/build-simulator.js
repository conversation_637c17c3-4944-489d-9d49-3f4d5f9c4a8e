#!/usr/bin/env node

// 模拟真实的 Vue 构建错误输出
const errors = [
  "ERROR in src/main.js:2:0-21",
  "Module not found: Error: Can't resolve 'element-ui' in '/Users/<USER>/src'",
  "",
  "ERROR in src/components/HelloWorld.vue:15:5",
  "Property 'message' does not exist on type 'ComponentPublicInstance'",
  "",
  "ERROR in src/router/index.js:3:0-35", 
  "Module not found: Error: Can't resolve 'vue-router' in '/Users/<USER>/src/router'",
  "",
  "src/main.js(5,1): error TS2304: Cannot find name 'Vue'.",
  "src/main.js(7,5): error TS2339: Property '$mount' does not exist on type 'App<Element>'.",
  "",
  "ERROR in src/views/Dashboard.vue:25:12",
  "Cannot find module '@/components/Chart' or its corresponding type declarations.",
  "",
  "webpack compiled with 8 errors",
  "",
  "Build failed with compilation errors.",
  "Please check the errors above and fix them before proceeding."
];

console.log(errors.join('\n'));
process.exit(1);
